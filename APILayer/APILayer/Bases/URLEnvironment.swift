//
//  URLEnvironment.swift
//  APILayer
//
//  Created by Chung <PERSON> on 14/11/2023.
//

import Foundation
import Networking
import Storage

// MARK: - URLEnvironment
public struct URLEnvironment: Equatable {
    public var url: String
    public var name: String
    public var version: String
    public var enableSSLPinning: Bool = false
    public var allowInsecureConnection: Bool = false
    public var debugMode: Bool
    public var identifier: String {
        return "\(name)-\(url)-\(version)"
    }

    public init(url: String,
                name: String,
                version: String,
                enableSSLPinning: Bool = false,
                allowInsecureConnection: Bool = false,
                debugMode: Bool = false) {
        self.url = url
        self.name = name
        self.version = version
        self.debugMode = debugMode
        self.enableSSLPinning = enableSSLPinning
        self.allowInsecureConnection = allowInsecureConnection
    }
}

public enum Environment {
    case dev
    case uat
    case prod
    
    var urlEnvironment: URLEnvironment {
        switch self {
        case .dev:
            return .init(url: "https://merit-dev.internal.siriustech.io",
                         name: "",
                         version: "",
                         enableSSLPinning: true,
                         allowInsecureConnection: true)
            
        case .uat:
            return .init(url: "https://merit-dev.internal.siriustech.io",
                         name: "",
                         version: "",
                         enableSSLPinning: true,
                         allowInsecureConnection: true)
            
        case .prod:
            return .init(url: "https://app.merit-am.com/api",
                         name: "",
                         version: "",
                         enableSSLPinning: true,
                         allowInsecureConnection: false)
        }
    }
    
    var wsEnvironment: WSConfiguration.URLEnvironment {
        switch self {
        case .dev:
            return .init(url: "wss://tradex-dev-wss.internal.siriustech.io/market/realtime",
                         cert: nil,
                         name: "DEV",
                         version: "0.0.1",
                         allowInsecureConnection: true)
        case .uat:
            return .init(url: "wss://tradex-dev-wss.internal.siriustech.io/market/realtime",
                         cert: nil,
                         name: "DEV",
                         version: "0.0.1",
                         allowInsecureConnection: true)
        case .prod:
            return .init(url: "https://tradex-demo-wss.internal.siriustech.io/market/realtime",
                         cert: nil,
                         name: "DEV",
                         version: "0.0.1",
                         allowInsecureConnection: true)
        }
    }
}

// MARK: - APIConstants
public class APIConstants {
    
    public static let shared = APIConstants()
    
    private var _baseUrl: URLEnvironment!
    private var customUrls = [String: URLEnvironment]()
    
    private init() {}
    
    public func configure(environment: Environment) {
        self._baseUrl = environment.urlEnvironment
    }
    
    public func configure(baseUrl: URLEnvironment) {
        self._baseUrl = baseUrl
    }
    
    public var baseUrl: URLEnvironment {
        return _baseUrl
    }
    
    public func registerCustomUrlFor<T: NetworkEndpoint>(endpoint: T, url: URLEnvironment) {
        customUrls[T.identifier] = url
    }
    
    public func urlFor<T: NetworkEndpoint>(endpoint: T) -> URLEnvironment {
        return customUrls[T.identifier, default: baseUrl]
    }
}
